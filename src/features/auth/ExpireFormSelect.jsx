import React, { useState, useEffect } from "react";
import { FormField, Select } from "semantic-ui-react";
import {
  getFirestore,
  collection,
  query,
  where,
  getDocs,
} from "firebase/firestore";
import { app } from "../../app/config/firebase";

const db = getFirestore(app);

export default function ExpireFormSelect({ selectedState, selectedForm, onFormChange }) {
  const [formOptions, setFormOptions] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchForms = async () => {
      if (!selectedState) {
        setFormOptions([]);
        return;
      }

      setLoading(true);
      try {
        const formsRef = collection(db, `forms${selectedState}`);
        const formsQuery = query(
          formsRef,
          where("dontShowFormInAddDocumentModal", "==", false)
        );
        const snapshot = await getDocs(formsQuery);
        
        const options = snapshot.docs
          .map((doc) => ({
            key: doc.data().id,
            value: doc.data().id,
            text: doc.data().title,
          }))
          .sort((a, b) => a.text.localeCompare(b.text));

        setFormOptions(options);
      } catch (error) {
        console.error("Error fetching forms:", error);
        setFormOptions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchForms();
  }, [selectedState]);

  return (
    <FormField>
      <label>Select Form to Expire</label>
      <Select
        placeholder="Select a form..."
        value={selectedForm || null}
        options={formOptions}
        onChange={(e, { value }) => onFormChange(value)}
        loading={loading}
        disabled={!selectedState || loading}
        search
        clearable
      />
    </FormField>
  );
}
